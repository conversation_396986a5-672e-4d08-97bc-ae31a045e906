#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AWVS JSON漏洞扫描报告解析器
解析AWVS导出的JSON文件，生成详细的Markdown格式报告
"""

import json
import re
from collections import defaultdict
from datetime import datetime

def extract_cve_numbers(text):
    """从文本中提取CVE编号"""
    if not text:
        return []
    cve_pattern = r'CVE-\d{4}-\d{4,7}'
    return re.findall(cve_pattern, str(text), re.IGNORECASE)

def classify_vulnerability_type(vuln_name, description="", tags=None):
    """根据漏洞名称和描述分类漏洞类型"""
    vuln_name = vuln_name.lower()
    description = description.lower() if description else ""
    tags = [tag.lower() for tag in (tags or [])]
    
    # SQL注入类
    if any(keyword in vuln_name for keyword in ['sql injection', 'sql注入']):
        return "SQL注入"
    
    # XSS类
    if any(keyword in vuln_name for keyword in ['cross-site scripting', 'xss', '跨站脚本']):
        return "跨站脚本(XSS)"
    
    # TLS/SSL类
    if any(keyword in vuln_name for keyword in ['tls', 'ssl', 'cipher', 'certificate', 'https']):
        return "TLS/SSL配置"
    
    # GraphQL类
    if 'graphql' in vuln_name:
        return "GraphQL配置"
    
    # JavaScript库类
    if any(keyword in vuln_name for keyword in ['javascript', 'jquery', 'bootstrap', 'moment.js', 'library', 'outdated']):
        return "JavaScript库"
    
    # 配置类
    if any(keyword in vuln_name for keyword in ['csp', 'content security policy', 'permissions-policy', 'hsts', 'cookie']):
        return "安全配置"
    
    # HTTP配置类
    if any(keyword in vuln_name for keyword in ['http', 'redirection', 'insecure']):
        return "HTTP配置"
    
    # 信息泄露类
    if any(keyword in vuln_name for keyword in ['information disclosure', 'prometheus', 'metrics', '信息泄露']):
        return "信息泄露"
    
    # CSRF类
    if 'csrf' in vuln_name or 'cross-site request forgery' in vuln_name:
        return "跨站请求伪造(CSRF)"
    
    # 拒绝服务类
    if any(keyword in vuln_name for keyword in ['denial of service', 'dos', 'overloading']):
        return "拒绝服务(DoS)"
    
    # 默认分类
    return "其他安全问题"

def main():
    # 文件路径
    json_file = "awvs/20250805_JSON_Multiple_targets.json"
    output_file = "awvs_vulnerability_report.md"
    
    # 严重等级映射
    severity_map = {
        4: "严重",
        3: "高危", 
        2: "中危",
        1: "低危",
        0: "信息"
    }
    
    print(f"开始解析文件: {json_file}")
    
    # 加载JSON数据
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("JSON文件加载成功")
    except Exception as e:
        print(f"加载JSON文件失败: {e}")
        return
    
    # 解析数据
    sites_data = []
    vulnerability_stats = defaultdict(lambda: {
        'sites': set(),
        'count': 0,
        'severity': '',
        'cves': set(),
        'urls': set()
    })
    
    scans = data['export']['scans']
    print(f"找到 {len(scans)} 个扫描站点")
    
    for scan in scans:
        site_info = {
            'host': scan['info']['host'],
            'start_url': scan['info']['start_url'],
            'duration': scan['info']['duration'],
            'vulnerabilities': [],
            'vuln_count_by_severity': {
                '严重': 0, '高危': 0, '中危': 0, '低危': 0, '信息': 0
            },
            'total_vulns': 0
        }
        
        # 处理漏洞类型
        vuln_types = {vt['vt_id']: vt for vt in scan.get('vulnerability_types', [])}
        
        # 处理具体漏洞实例
        for vuln in scan.get('vulnerabilities', []):
            vuln_info = vuln['info']
            vt_id = vuln_info['vt_id']
            
            if vt_id in vuln_types:
                vt = vuln_types[vt_id]
                severity_num = vt['severity']
                severity_cn = severity_map.get(severity_num, "未知")
                
                # 提取CVE编号
                cves = []
                for field in ['description', 'recommendation', 'long_description']:
                    if field in vt:
                        cves.extend(extract_cve_numbers(vt[field]))
                
                # 从tags中提取CVE
                if 'tags' in vt:
                    for tag in vt['tags']:
                        cves.extend(extract_cve_numbers(tag))
                
                vuln_detail = {
                    'name': vt['name'],
                    'severity': severity_cn,
                    'severity_num': severity_num,
                    'url': vuln_info['url'],
                    'cves': list(set(cves)),
                    'cvss_score': vt.get('cvss_score', 0),
                    'description': vt.get('description', ''),
                    'tags': vt.get('tags', [])
                }
                
                site_info['vulnerabilities'].append(vuln_detail)
                site_info['vuln_count_by_severity'][severity_cn] += 1
                site_info['total_vulns'] += 1
                
                # 统计全局漏洞类型
                vuln_type = classify_vulnerability_type(
                    vt['name'], vt.get('description', ''), vt.get('tags', [])
                )
                
                vulnerability_stats[vuln_type]['sites'].add(site_info['host'])
                vulnerability_stats[vuln_type]['count'] += 1
                vulnerability_stats[vuln_type]['severity'] = severity_cn
                vulnerability_stats[vuln_type]['cves'].update(cves)
                vulnerability_stats[vuln_type]['urls'].add(vuln_info['url'])
        
        sites_data.append(site_info)
    
    # 按漏洞总数排序
    sites_data.sort(key=lambda x: x['total_vulns'], reverse=True)
    
    print(f"解析完成，共处理 {len(sites_data)} 个站点")
    
    # 生成报告
    report = []
    
    # 报告标题
    report.append("# AWVS漏洞扫描报告")
    report.append("")
    report.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"**扫描站点数**: {len(sites_data)}")
    
    total_vulns = sum(site['total_vulns'] for site in sites_data)
    report.append(f"**漏洞总数**: {total_vulns}")
    report.append("")
    
    # 表格1：站点漏洞概览表
    report.append("## 1. 站点漏洞概览表")
    report.append("")
    report.append("| 序号 | 站点URL | 漏洞总数 | 严重 | 高危 | 中危 | 低危 | 信息 |")
    report.append("|------|---------|----------|------|------|------|------|------|")
    
    for i, site in enumerate(sites_data, 1):
        counts = site['vuln_count_by_severity']
        report.append(f"| {i} | {site['host']} | {site['total_vulns']} | {counts['严重']} | {counts['高危']} | {counts['中危']} | {counts['低危']} | {counts['信息']} |")
    
    report.append("")
    
    # 保存第一部分报告
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(report))
        print(f"报告第一部分已生成: {output_file}")
    except Exception as e:
        print(f"保存报告失败: {e}")
        return
    
    # 表格2：站点详细漏洞清单
    report.append("## 2. 站点详细漏洞清单")
    report.append("")

    for site in sites_data:
        report.append(f"### {site['host']}")
        report.append("")

        if site['vulnerabilities']:
            report.append("| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |")
            report.append("|----------|----------|----------|---------|----------|")

            # 按漏洞名称分组统计
            vuln_groups = defaultdict(lambda: {
                'count': 0,
                'severity': '',
                'cves': set(),
                'urls': set()
            })

            for vuln in site['vulnerabilities']:
                name = vuln['name']
                vuln_groups[name]['count'] += 1
                vuln_groups[name]['severity'] = vuln['severity']
                vuln_groups[name]['cves'].update(vuln['cves'])
                vuln_groups[name]['urls'].add(vuln['url'])

            for name, info in vuln_groups.items():
                cve_str = ', '.join(sorted(info['cves'])) if info['cves'] else '-'
                url_str = ', '.join(sorted(info['urls']))[:100] + ('...' if len(', '.join(sorted(info['urls']))) > 100 else '')
                report.append(f"| {name} | {info['count']} | {info['severity']} | {cve_str} | {url_str} |")
        else:
            report.append("| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |")
            report.append("|----------|----------|----------|---------|----------|")
            report.append("| 无漏洞 | 0 | - | - | - |")

        report.append("")

    # 表格3：严重+高危漏洞统计表
    report.append("## 3. 严重+高危漏洞统计表")
    report.append("")
    report.append("| 漏洞类型 | 影响站点数 | 漏洞总数 | 主要CVE编号 | 代表性站点 |")
    report.append("|----------|------------|----------|-------------|------------|")

    high_critical_vulns = {}
    for vuln_type, stats in vulnerability_stats.items():
        if stats['severity'] in ['严重', '高危']:
            high_critical_vulns[vuln_type] = stats

    # 按漏洞总数排序
    for vuln_type in sorted(high_critical_vulns.keys(), key=lambda x: high_critical_vulns[x]['count'], reverse=True):
        stats = high_critical_vulns[vuln_type]
        sites_count = len(stats['sites'])
        cve_str = ', '.join(sorted(list(stats['cves']))[:3]) if stats['cves'] else '-'
        if len(stats['cves']) > 3:
            cve_str += '...'
        sample_sites = ', '.join(sorted(list(stats['sites']))[:2])
        if len(stats['sites']) > 2:
            sample_sites += '...'

        report.append(f"| {vuln_type} | {sites_count} | {stats['count']} | {cve_str} | {sample_sites} |")

    if not high_critical_vulns:
        report.append("| 无严重或高危漏洞 | 0 | 0 | - | - |")

    report.append("")

    # 表格4：中危+低危+信息类漏洞统计表
    report.append("## 4. 中危+低危+信息类漏洞统计表")
    report.append("")
    report.append("| 漏洞类型 | 影响站点数 | 漏洞总数 | 主要CVE编号 | 代表性站点 |")
    report.append("|----------|------------|----------|-------------|------------|")

    medium_low_info_vulns = {}
    for vuln_type, stats in vulnerability_stats.items():
        if stats['severity'] in ['中危', '低危', '信息']:
            medium_low_info_vulns[vuln_type] = stats

    # 按漏洞总数排序
    for vuln_type in sorted(medium_low_info_vulns.keys(), key=lambda x: medium_low_info_vulns[x]['count'], reverse=True):
        stats = medium_low_info_vulns[vuln_type]
        sites_count = len(stats['sites'])
        cve_str = ', '.join(sorted(list(stats['cves']))[:3]) if stats['cves'] else '-'
        if len(stats['cves']) > 3:
            cve_str += '...'
        sample_sites = ', '.join(sorted(list(stats['sites']))[:2])
        if len(stats['sites']) > 2:
            sample_sites += '...'

        report.append(f"| {vuln_type} | {sites_count} | {stats['count']} | {cve_str} | {sample_sites} |")

    if not medium_low_info_vulns:
        report.append("| 无中危、低危或信息类漏洞 | 0 | 0 | - | - |")

    report.append("")

    # 扫描器覆盖说明
    report.append("## 扫描器覆盖说明")
    report.append("")
    report.append("本次扫描使用AWVS (Acunetix Web Vulnerability Scanner) 进行，扫描覆盖以下安全检测项目：")
    report.append("")
    report.append("- **Web应用漏洞检测**: SQL注入、XSS、CSRF等常见Web漏洞")
    report.append("- **配置安全检测**: TLS/SSL配置、HTTP安全头、CSP策略等")
    report.append("- **信息泄露检测**: 敏感信息暴露、目录遍历等")
    report.append("- **第三方组件检测**: JavaScript库版本、已知CVE漏洞等")
    report.append("- **GraphQL安全检测**: GraphQL特有的安全配置问题")
    report.append("")

    # 保存完整报告
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(report))
        print(f"完整报告已生成: {output_file}")
        print(f"报告包含 {len(sites_data)} 个站点，共 {total_vulns} 个漏洞")
    except Exception as e:
        print(f"保存报告失败: {e}")

if __name__ == '__main__':
    main()
