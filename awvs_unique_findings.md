# AWVS独有漏洞发现清单

> 注：本报告仅包含AWVS扫描发现但ASM未发现的漏洞，并已排除167.71.5.207站点的所有结果

## 1. 按站点统计

### metabase.status200.io:8085
| 漏洞名称 | 严重等级 | CVE编号 |
|---------|----------|---------|
| GraphQL Alias Overloading | 中危 | - |
| GraphQL Array-based Query Batching | 中危 | - |
| GraphQL Circular-Query via Introspection | 中危 | - |
| GraphQL Field Suggestions Enabled | 中危 | - |
| GraphQL Introspection Query Enabled | 中危 | - |
| GraphQL Non-JSON Queries over GET | 中危 | - |
| Unchecked GraphQL Query Length | 中危 | - |
| Outdated JavaScript libraries | 信息 | - |
| Permissions-Policy header not implemented | 信息 | - |
| Content Security Policy (CSP) Not Implemented | 信息 | - |
| default-src Used in CSP | 信息 | - |

### testweb.videoracing.com
| 漏洞名称 | 严重等级 | CVE编号 |
|---------|----------|---------|
| Vulnerable JavaScript libraries | 中危 | - |
| Version Disclosure (ASP.NET MVC) | 低危 | - |
| Version Disclosure (ASP.NET) | 低危 | - |
| Content Security Policy (CSP) Not Implemented | 信息 | - |
| Reverse Proxy Detected | 信息 | - |
| Web Application Firewall Detected | 信息 | - |

### bos.00387.info:10000
| 漏洞名称 | 严重等级 | CVE编号 |
|---------|----------|---------|
| Clickjacking: CSP frame-ancestors missing | 低危 | - |
| An Unsafe Content Security Policy (CSP) Directive in Use | 信息 | - |
| Content Security Policy (CSP) Not Implemented | 信息 | - |
| Missing object-src in CSP Declaration | 信息 | - |

### tinylima.status200.io
| 漏洞名称 | 严重等级 | CVE编号 |
|---------|----------|---------|
| Clickjacking: CSP frame-ancestors missing | 低危 | - |
| Missing object-src in CSP Declaration | 信息 | - |
| Permissions-Policy header not implemented | 信息 | - |

### res.quantum-nexus.net:8081
| 漏洞名称 | 严重等级 | CVE编号 |
|---------|----------|---------|
| Programming Error Messages | 低危 | - |
| Content Security Policy (CSP) Not Implemented | 信息 | - |

### tucasinoencasa.com:9100
| 漏洞名称 | 严重等级 | CVE编号 |
|---------|----------|---------|
| Golang runtime profiling data | 中危 | - |
| Content Security Policy (CSP) Not Implemented | 信息 | - |
| Permissions-Policy header not implemented | 信息 | - |

### h5admin.zsmeme.com
| 漏洞名称 | 严重等级 | CVE编号 |
|---------|----------|---------|
| Content Security Policy (CSP) Not Implemented | 信息 | - |
| Permissions-Policy header not implemented | 信息 | - |
| Reverse Proxy Detected | 信息 | - |
| Web Application Firewall Detected | 信息 | - |

### www.cod9988.net
| 漏洞名称 | 严重等级 | CVE编号 |
|---------|----------|---------|
| Content Security Policy (CSP) Not Implemented | 信息 | - |
| Permissions-Policy header not implemented | 信息 | - |

## 2. 漏洞类型统计

| 漏洞类型 | 数量 | 严重等级 |
|---------|------|----------|
| GraphQL相关配置问题 | 7 | 中危 |
| CSP配置缺失或错误 | 8 | 低危/信息 |
| 权限策略配置缺失 | 4 | 信息 |
| 框架/库版本问题 | 3 | 中危/低危 |
| 代理/WAF检测 | 4 | 信息 |
| 其他配置问题 | 5 | 低危/信息 |

## 3. 主要特点分析

1. **GraphQL安全**
   - AWVS在GraphQL安全检测方面表现突出
   - 发现了多个潜在的GraphQL相关安全问题
   - 主要集中在metabase.status200.io:8085站点

2. **安全头配置检查**
   - 更注重CSP和Permissions-Policy等安全头的检查
   - 几乎对所有站点进行了安全头配置检查
   - 提供了具体的配置改进建议

3. **技术栈识别**
   - 能够识别更多的技术组件版本信息
   - 对框架级别的安全问题有更深入的检测
   - 包括JavaScript库、ASP.NET等组件的版本检测

4. **基础设施检测**
   - 能够识别反向代理和WAF的存在
   - 对运行时环境（如Golang）的安全配置有更细致的检查
